﻿# 📁 Structure du Projet - Rotary Club Mobile

## Déplacement vers C:\MOBILE\RotaryClubMobile

Le projet a été déplacé de C:\RotaryClubMobile vers C:\MOBILE\RotaryClubMobile le 17 juin 2025 pour suivre les standards de développement Windows.

### Avantages de cette structure :

1. **Organisation centralisée** : Tous les projets mobiles dans C:\MOBILE\
2. **Standards Windows** : Respect des conventions de développement Windows  
3. **Séparation claire** : Distinction entre projets mobiles et autres projets
4. **Facilité de maintenance** : Structure plus claire pour les développeurs

### Commandes de développement :

`ash
# Naviguer vers le projet
cd C:\MOBILE\RotaryClubMobile

# Installer les dépendances
npm install

# Lancer le serveur de développement
npx expo start
`

---
*Dernière mise à jour : 17 juin 2025*
