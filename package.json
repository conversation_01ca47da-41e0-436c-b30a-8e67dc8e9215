{"name": "rotary-club-snack", "version": "1.0.0", "main": "App.tsx", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~52.0.0", "expo-status-bar": "~2.0.1", "react": "18.3.1", "react-native": "0.76.1", "@expo/vector-icons": "^14.0.2", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-gesture-handler": "~2.20.2", "expo-secure-store": "~14.0.1"}, "devDependencies": {"@babel/core": "^7.20.0", "typescript": "^5.1.3"}, "private": true}