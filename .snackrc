{"name": "Rotary Club Mobile", "description": "Application mobile complète pour les clubs Rotary avec React Native, Redux et Material Design", "sdkVersion": "49.0.0", "platforms": ["ios", "android", "web"], "entryPoint": "App.tsx", "dependencies": {"expo": "~49.0.0", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/bottom-tabs": "^6.5.11", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "react-native-paper": "^5.12.1", "@expo/vector-icons": "^13.0.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-gesture-handler": "~2.12.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "typescript": "^5.1.3"}, "expo": {"name": "Rotary Club Mobile", "slug": "rotary-club-mobile", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "light", "splash": {"resizeMode": "contain", "backgroundColor": "#005AA9"}, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"backgroundColor": "#005AA9"}}, "web": {"bundler": "metro"}}}