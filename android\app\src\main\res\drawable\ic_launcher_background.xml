<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- Background gradient with Rotary colors -->
    <path
        android:fillType="evenOdd"
        android:pathData="M0,0h108v108h-108z">
        <aapt:attr name="android:fillColor" xmlns:aapt="http://schemas.android.com/aapt">
            <gradient
                android:startColor="#005AA9"
                android:endColor="#003D75"
                android:type="linear"
                android:angle="135" />
        </aapt:attr>
    </path>
    
    <!-- Subtle pattern -->
    <path
        android:fillColor="#10FFFFFF"
        android:pathData="M54,20 L54,88 M20,54 L88,54"
        android:strokeWidth="1"
        android:strokeColor="#20FFFFFF" />
        
</vector>
